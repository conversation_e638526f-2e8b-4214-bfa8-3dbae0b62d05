<?php

use function PHPSTORM_META\type;

class Quiz_model extends CI_Model
{
	function quiz_list($limit, $stat = '', $group = [])
	{
		$logged_in = $this->session->userdata('logged_in');
		$userData = $this->user_model->get_user($logged_in['uid']);
		$acp = explode(',', $logged_in['quiz']);
		$gid = $userData['gid'];
		$uid = $userData['uid'];
		$this->db->select('*');
		$this->db->select('savsoft_quiz.quid as squid');
		/* if (!in_array('List_all', $acp)) {
			$query = $this->db->query("select * from savsoft_users where uid='$uid' ");
			$user = $query->row_array();
			$gid = explode(',', $user['gid']);
			$vgid = implode('|', $gid);
			$where="FIND_IN_SET('".$gid."', gids) or FIND_IN_SET('".$uid."', uids)";
			$where = 'CONCAT(",",gids, ",") REGEXP ",(' . $vgid . '),"';
			$this->db->where($where);
		} */
		if ($this->input->get('search') && in_array('List_all', $acp)) {
			$search = $this->input->get('search');
			$this->db->where('savsoft_quiz.quid', $search);
			$this->db->or_like('quiz_name', $search);
			//$this->db->or_like('description', $search);
		}
		/* if ($userData['uid'] != '1' && $userData['inserted_by'] != '0') {
			$uid=$userData['inserted_by'];
	 		$this->db->where('savsoft_quiz.inserted_by',$uid);
		} */
		if ($stat == "active") {
			$where = ' end_date >= "' . time() . '" ';
			$this->db->where($where);
			$this->db->or_where('end_date',0);
		}
		if ($stat == "archived") {
			$where = ' end_date < "' . time() . '" ';
			$this->db->where($where);
		}
		if ($stat == "upcoming") {
			$where = ' start_date >= "' . time() . '" ';
			$this->db->where($where);
		}
		if (!in_array('List_all', $acp)) {
			$where="FIND_IN_SET('".$gid."', gids) or FIND_IN_SET('".$uid."', uids)";
			$this->db->where($where);
		}
		$type = $this->input->get('type');
		if($type != "0" && $type){
			if($type == "random") {
				$this->db->where('qids', '');
			} else {
				$this->db->where('qids !=', '');
			}
		}
		if($group) {
			$this->db->group_start();
			foreach($group as $val) {
				$where="FIND_IN_SET('".$val."', gids)";
				$this->db->or_where($where);
			}
			$this->db->group_end();
		}
		if (!in_array('List_all', $acp)) {
			//$this->db->limit($this->config->item('number_of_rows'), $limit);
			// $this->db->join('savsoft_result','savsoft_result.quid = savsoft_quiz.quid','left');
			// $this->db->group_by('savsoft_result.quid');
			// $this->db->order_by('result_count', 'desc');
			$this->db->join('quiz_order','quiz_order.exam_id = savsoft_quiz.quid and quiz_order.group_id='.$gid, 'left');
			$this->db->group_by('savsoft_quiz.quid');
			$this->db->order_by('order_number', 'asc');
		}else{
			$this->db->order_by('savsoft_quiz.quid', 'desc');
		}
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	function get_num_quiz_done()
	{
		$logged_in = $this->session->userdata('logged_in');
		$userData = $this->user_model->get_user($logged_in['uid']);
		$uid = $logged_in['uid'];
		$acp = explode(',', $logged_in['quiz']);
		$this->db->select('savsoft_quiz.quid');
		$this->db->select('count(savsoft_result.quid) as result_count');
		$this->db->join('savsoft_result','savsoft_result.quid = savsoft_quiz.quid','left');
		if (!in_array('List_all', $acp)) {
			$this->db->where('savsoft_result.uid',$uid);
		}
		$this->db->where('savsoft_result.result_status !=',"Open");
		$this->db->group_by('savsoft_result.quid');
		$this->db->order_by('end_date', 'asc');
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	function user_quiz_detail_fail(){
		$logged_in = $this->session->userdata('logged_in');
		$userData = $this->user_model->get_user($logged_in['uid']);
		$acp = explode(',', $logged_in['quiz']);
		$uid = $userData['uid'];
		$this->db->select('savsoft_quiz.quid');
		$this->db->select('count(savsoft_result.quid) as result_count');
		$this->db->join('savsoft_result','savsoft_result.quid = savsoft_quiz.quid','left');
		$this->db->where('savsoft_result.result_status',"Fail");
		if (!in_array('List_all', $acp)) {
			$this->db->where('savsoft_result.uid',$uid);
		}
		//$this->db->where($where);
		$this->db->group_by('savsoft_result.quid');
		//$this->db->order_by('end_date', 'asc');
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	function user_quiz_detail_pass(){
		$logged_in = $this->session->userdata('logged_in');
		$userData = $this->user_model->get_user($logged_in['uid']);
		$acp = explode(',', $logged_in['quiz']);
		$uid = $userData['uid'];
		//$where = "FIND_IN_SET('" . $uid . "', uids)";
		$this->db->select('savsoft_quiz.quid');
		$this->db->select('count(savsoft_result.quid) as result_count');
		$this->db->join('savsoft_result','savsoft_result.quid = savsoft_quiz.quid','left');
		$this->db->where('savsoft_result.result_status',"Pass");
		//$this->db->where($where);
		if (!in_array('List_all', $acp)) {
			$this->db->where('savsoft_result.uid',$uid);
		}
		$this->db->group_by('savsoft_result.quid');
		$this->db->order_by('end_date', 'asc');
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	function quizstat($stat)
	{
		$logged_in = $this->session->userdata('logged_in');
		$acp = explode(',', $logged_in['quiz']);
		$uid = $logged_in['uid'];
		//$where = "FIND_IN_SET('" . $uid . "', uids)";
		$this->db->select('savsoft_quiz.quid');
		$this->db->select('count(savsoft_result.quid) as result_count');
		$this->db->join('savsoft_result','savsoft_result.quid = savsoft_quiz.quid','left');
		$this->db->where('savsoft_result.result_status',"Fail");
		//$this->db->where($where);
		if (!in_array('List_all', $acp)) {
			$this->db->where('savsoft_result.uid',$uid);
		}
		$this->db->group_by('savsoft_result.quid');
		$this->db->order_by('end_date', 'asc');
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	function recent_quiz($limit)
	{
		$this->db->limit($limit);
		$this->db->order_by('quid', 'desc');
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	function open_quiz($limit)
	{
		$this->db->limit($this->config->item('number_of_rows'), $limit);
		$this->db->order_by('quid', 'desc');
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	function num_quiz()
	{
		$logged_in = $this->session->userdata('logged_in');
		if ($logged_in['uid'] != '1') {
			/* $uid=$logged_in['uid'];
	 		$this->db->where('savsoft_quiz.inserted_by',$uid); */
		}
		$query = $this->db->get('savsoft_quiz');
		return $query->num_rows();
	}
	function num_quiz_list($stat = '', $group = [])
	{
		$logged_in = $this->session->userdata('logged_in');
		$acp = explode(',', $logged_in['quiz']);
		$gid = $logged_in['gid'];
		$uid = $logged_in['uid'];
		$this->db->select('*');
		$this->db->select('savsoft_quiz.quid as squid');
		/* if (!in_array('List_all', $acp)) {
			$query = $this->db->query("select * from savsoft_users where uid='$uid' ");
			$user = $query->row_array();
			$gid = explode(',', $user['gid']);
			$vgid = implode('|', $gid);
			$where="FIND_IN_SET('".$gid."', gids) or FIND_IN_SET('".$uid."', uids)";
			$where = 'CONCAT(",",gids, ",") REGEXP ",(' . $vgid . '),"';
			$this->db->where($where);
		} */
		if ($this->input->get('search') && in_array('List_all', $acp)) {
			$search = $this->input->get('search');
			$this->db->or_where('savsoft_quiz.quid', $search);
			$this->db->or_like('quiz_name', $search);
			// $this->db->or_like('description', $search);
		}
		/* if ($logged_in['uid'] != '1' && $logged_in['inserted_by'] != '0') {
			$uid=$logged_in['inserted_by'];
	 		$this->db->where('savsoft_quiz.inserted_by',$uid);
		} */
		if ($stat == "active") {
			$where = ' end_date >= "' . time() . '" ';
			$this->db->where($where);
			$this->db->or_where('end_date',0);
		}
		if ($stat == "archived") {
			$where = ' end_date < "' . time() . '" ';
			$this->db->where($where);
		}
		if ($stat == "upcoming") {
			$where = ' start_date >= "' . time() . '" ';
			$this->db->where($where);
		}
		if (!in_array('List_all', $acp)) {
			$where="FIND_IN_SET('".$gid."', gids) or FIND_IN_SET('".$uid."', uids)";
			$this->db->where($where);
		}
		$type = $this->input->get('type');
		if($type != "0"){
			if($type == "random") {
				$this->db->where('qids', '');
			} else {
				$this->db->where('qids !=', '');
			}
		}	
		if($group) {
			$this->db->group_start();
			foreach($group as $val) {
				$where="FIND_IN_SET('".$val."', gids)";
				$this->db->or_where($where);
			}
			$this->db->group_end();
		}
		$query = $this->db->get('savsoft_quiz');
		return $query->num_rows();
	}
	function insert_quiz()
	{
		if(!$this->input->post('uids'))
		$r_uid = [];
		else
		$r_uid = $this->input->post('uids');
		if(!$this->input->post('gids'))
		$r_qid = [];
		else
		$r_qid = $this->input->post('gids');
		$userdata = array(
			'quiz_name' => $this->input->post('quiz_name'),
			'description' => $this->input->post('description'),
			'start_date' => strtotime($this->input->post('start_date')),
			'end_date' => strtotime($this->input->post('end_date')),
			'duration' => $this->input->post('duration'),
			'maximum_attempts' => $this->input->post('maximum_attempts'),
			'pass_percentage' => $this->input->post('pass_percentage'),
			'correct_score' => $this->input->post('correct_score'),
			'incorrect_score' => $this->input->post('incorrect_score'),
			'ip_address' => $this->input->post('ip_address'),
			'view_answer' => $this->input->post('view_answer'),
			'camera_req' => $this->input->post('camera_req'),
			'quiz_template' => $this->input->post('quiz_template'),
			'quiz_price' => $this->input->post('quiz_price'),
			'with_login' => $this->input->post('with_login'),
			'show_chart_rank' => $this->input->post('show_chart_rank'),
			'gids' => implode(',', $r_qid),
			'uids' => implode(',', $r_uid),
			'question_selection' => $this->input->post('question_selection'),
			'theme' => $this->input->post('theme'),
			'exam_quiz' => $this->input->post('exam_quiz'),
		);
		$userdata['gen_certificate'] = $this->input->post('gen_certificate');
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		$fname = $logged_in['name'];
		$userdata['inserted_by'] = $uid;
		$userdata['inserted_by_name'] = $fname;
		if ($this->input->post('certificate_text')) {
			$userdata['certificate_text'] = $this->input->post('certificate_text');
		}
		$this->db->insert('savsoft_quiz', $userdata);
		$quid = $this->db->insert_id();
		return $quid;
	}
	function update_quiz($quid)
	{
		if(!$this->input->post('uids'))
		$r_uid = [];
		else
		$r_uid = $this->input->post('uids');
		if(!$this->input->post('gids'))
		$r_qid = [];
		else
		$r_qid = $this->input->post('gids');
		$userdata = array(
			'quiz_name' => $this->input->post('quiz_name'),
			'description' => $this->input->post('description'),
			'start_date' => strtotime($this->input->post('start_date')),
			'end_date' => strtotime($this->input->post('end_date')),
			'duration' => $this->input->post('duration'),
			'maximum_attempts' => $this->input->post('maximum_attempts'),
			'pass_percentage' => $this->input->post('pass_percentage'),
			//'correct_score' => $this->input->post('correct_score'),
			//'incorrect_score' => $this->input->post('incorrect_score'),
			'ip_address' => $this->input->post('ip_address'),
			'view_answer' => $this->input->post('view_answer'),
			'camera_req' => $this->input->post('camera_req'),
			'quiz_template' => $this->input->post('quiz_template'),
			'quiz_price' => $this->input->post('quiz_price'),
			'with_login' => $this->input->post('with_login'),
			'show_chart_rank' => $this->input->post('show_chart_rank'),
			'gids' => implode(',', $r_qid),
			'uids' => implode(',', $r_uid),
			'theme' => $this->input->post('theme'),
			'exam_quiz' => $this->input->post('exam_quiz'),
		);
		$userdata['gen_certificate'] = $this->input->post('gen_certificate');
		if ($this->input->post('certificate_text')) {
			$userdata['certificate_text'] = $this->input->post('certificate_text');
		}
		$this->db->where('quid', $quid);
		$this->db->update('savsoft_quiz', $userdata);
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_quiz');
		$quiz = $query->row_array();
		if ($quiz['question_selection'] == '1') {
			$this->db->where('quid', $quid);
			$this->db->delete('savsoft_qcl');
			$correct_i = array();
			$incorrect_i = array();
			foreach ($_POST['cid'] as $ck => $val) {
				if (isset($_POST['noq'][$ck])) {
					if ($_POST['noq'][$ck] >= '1') {
						if($_POST['lid'.$ck] == null){
							$new_lid = '';
						}else{
							$new_lid = implode(",",$_POST['lid'.$ck]);
						}
						$userdata = array(
							'quid' => $quid,
							'cid' => $val,
							'lid' => $new_lid,
							'i_correct' => $_POST['i_correct'][$ck],
							'i_incorrect' => $_POST['i_incorrect'][$ck],
							'noq' => $_POST['noq'][$ck]
						);
						$this->db->insert('savsoft_qcl', $userdata);
						for ($i = 1; $i <= $_POST['noq'][$ck]; $i++) {
							$correct_i[] = $_POST['i_correct'][$ck];
							$incorrect_i[] = $_POST['i_incorrect'][$ck];
						}
					}
				}
			}
			$total = 0;
			foreach($_POST['noq'] as $no => $q){
				$total += (int) $q;
			}
			$userdata = array(
				'noq' => $total,
				'correct_score' => implode(',', $correct_i),
				'incorrect_score' => implode(',', $incorrect_i)
			);
			$this->db->where('quid', $quid);
			$this->db->update('savsoft_quiz', $userdata);
		} else {
			$correct_i = array();
			$incorrect_i = array();
			foreach ($_POST['i_correct'] as $ck => $cv) {
				$correct_i[] = $_POST['i_correct'][$ck];
				$incorrect_i[] = $_POST['i_incorrect'][$ck];
			}
			$status = false;
			foreach($correct_i as $a => $b){
				if($b == ""){
					$correct_i[$a] = "1";
					$incorrect_i[$a] = "0";
					$status = true;
				}
			}
			if(implode(",",$_POST['i_incorrect']) != $quiz['incorrect_score'] || implode(",",$_POST['i_correct']) != $quiz['correct_score'] || count(explode(",",$_POST['correct_score'])) == 1 || count(explode(",",$_POST['incorrect_score'])) == 1 || $status == true){
				$userdata = array(
					'correct_score' => implode(',', $correct_i),
					'incorrect_score' => implode(',', $incorrect_i)
				);
			}else{
				$userdata = array(
					'correct_score' => $_POST['correct_score'],
					'incorrect_score' => $_POST['incorrect_score']
				);
			}
			$this->db->where('quid', $quid);
			$this->db->update('savsoft_quiz', $userdata);

			// update lại r_qids
			if($_POST['cid'] != 0 && $_POST['noq'] != null){
				$total = 0;
				foreach($_POST['noq'] as $no => $q){
					$total += (int) $q;
				}
				$this->db->select('qid');
				$this->db->where("cid", $_POST['cid'][0]);
				$this->db->where('status', 1);
				if($_POST['lid'] != null){
					foreach($_POST['lid'] as $arr => $lids){
						$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
					}
				}
				$this->db->order_by('RAND()');
				$this->db->limit($total);
				$query = $this->db->get('savsoft_qbank');
				$questions = $query->result_array();
				$qid_array = array_column($questions, 'qid');
				$qid_string = implode(",", $qid_array);
				$this->db->where('quid', $quid);
				$this->db->update('savsoft_quiz', ['qids'=> implode(',',$qid_array), 'noq' => $total]);
			}else{
				$qids = $quiz['qids'];
				$listQids = explode(',',$qids);
				foreach($listQids as $val) {
					$count = $this->db->where('qid', $val)->select('qid')->get('savsoft_qbank')->result_array();
					if(count($count) == 0) {
						$key = array_search($val, $listQids);
						unset($listQids[$key]);
					}
				}
				$this->db->where('quid', $quid);
				$this->db->update('savsoft_quiz', ['qids'=> implode(',',$listQids), 'noq' => count($listQids)]);
			}
		}
		
		return $quid;
	}
	function remove_all_questions($quid){
		$this->db->where('quid', $quid);
		$this->db->update('savsoft_quiz', ['qids'=> "", 'noq' => 0]);
	}

	function get_quiz_transactions($quid)
	{
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		$this->db->where('quid', $quid);
		$this->db->where('uid', $uid);
		$this->db->where('payment_status', 'Paid');
		$query = $this->db->get('savsoft_payment');
		return $query->num_rows();
	}
	function get_purchased_quiz()
	{
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		$this->db->where('uid', $uid);
		$this->db->where('payment_status', 'Paid');
		$query = $this->db->get('savsoft_payment');
		$result = $query->result_array();
		$pquid = array();
		foreach ($result as $k => $val) {
			$pquid[] = $val['quid'];
		}
		return $pquid;
	}
	function get_questions($qids, $isApp = false)
	{
		if ($qids == '') {
			$qids = 0;
		} else {
			$qids = $qids;
		}
		$query = $this->db->query("select *, SUBSTRING(description, 1, 100) AS shortened_description from savsoft_qbank where savsoft_qbank.qid in ($qids) and savsoft_qbank.status != 0 order by FIELD(savsoft_qbank.qid,$qids)");
		$listQuestions =  $query->result_array();
		if(!$isApp){
			foreach($listQuestions as $key => $val){
				$difficultyStr = $this->result_model->getDifficultyStr($val);
				$listQuestions[$key]["difficulty"] = $difficultyStr;
			}
		}
		return $listQuestions;
	}
	function get_question($qid)
	{
		$this->db->where('qid', $qid);
		$query = $this->db->get('savsoft_qbank');
		return $query->row_array(); 

	}

	function get_questions_exam_tool($qids)
	{
		if ($qids == '') {
			$qids = 0;
		} else {
			$qids = $qids;
		}
		/*
	 if($cid!='0'){
		 $this->db->where('savsoft_qbank.cid',$cid);
	 }
	 if($lid!='0'){
		 $this->db->where('savsoft_qbank.lid',$lid);
	 }
*/
		$query = $this->db->query("select qid,question_type,question,description,no_time_corrected,status,no_time_served,no_time_unattempted,no_time_incorrected from savsoft_qbank
	 where savsoft_qbank.qid in ($qids) order by FIELD(savsoft_qbank.qid,$qids)
	 ");
		return $query->result_array();
	}
	public function get_questions_edit_quiz($qids)
	{
		if ($qids == '') {
			$qids = 0;
		} else {
			$qids = $qids;
		}
		$query = $this->db->query("select * from savsoft_qbank
	 where savsoft_qbank.qid in ($qids) order by FIELD(savsoft_qbank.qid,$qids)
	 ");
		return $query->result_array();
	}
	function get_options($qids)
	{
		$query = $this->db->query("select * from savsoft_options where qid in ($qids) order by FIELD(savsoft_options.qid,$qids), oid ASC");
		return $query->result_array();
	}
	function get_options_v2($qids)
	{
		$query = $this->db->query("SELECT savsoft_options.*, COUNT(savsoft_answers.qid) AS num_selected FROM savsoft_options LEFT JOIN savsoft_answers ON savsoft_answers.qid = savsoft_options.qid AND savsoft_answers.q_option = savsoft_options.oid WHERE savsoft_options.qid IN ($qids) GROUP BY savsoft_options.qid, savsoft_options.oid ORDER BY FIELD(savsoft_options.qid, $qids), oid ASC;");
		return $query->result_array();
	}
	function get_options_v3($qids)
	{
		$query = $this->db->query("select oid,q_option,score from savsoft_options where qid in ($qids) order by FIELD(savsoft_options.qid,$qids), oid ASC");
		return $query->result_array();
	}
	function get_options_exam_tool($qids)
	{
		$query = $this->db->query("select oid,qid,q_option,score from savsoft_options where qid in ($qids) order by FIELD(savsoft_options.qid,$qids), oid ASC");
		return $query->result_array();
	}
	function get_correct_options($qids)
	{
		$query = $this->db->query("select * from savsoft_options where qid in ($qids) order by FIELD(savsoft_options.qid,$qids), oid ASC");
		return $query->result_array();
	}
	function get_description($qid){
		$this->db->select("description");
		$this->db->where('qid', $qid);
		$query = $this->db->get("savsoft_qbank");
		return $query->row()->description;
	}
	function up_question($quid, $qid)
	{
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_quiz');
		$result = $query->row_array();
		$qids = $result['qids'];
		if ($qids == "") {
			$qids = array();
		} else {
			$qids = explode(",", $qids);
		}
		$qids_new = array();
		foreach ($qids as $k => $qval) {
			if ($qval == $qid) {
				$qids_new[$k - 1] = $qval;
				$qids_new[$k] = $qids[$k - 1];
			} else {
				$qids_new[$k] = $qval;
			}
		}
		$qids = array_filter(array_unique($qids_new));
		$qids = implode(",", $qids);
		$userdata = array(
			'qids' => $qids
		);
		$this->db->where('quid', $quid);
		$this->db->update('savsoft_quiz', $userdata);
	}
	function down_question($quid, $qid)
	{
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_quiz');
		$result = $query->row_array();
		$qids = $result['qids'];
		if ($qids == "") {
			$qids = array();
		} else {
			$qids = explode(",", $qids);
		}
		$qids_new = array();
		foreach ($qids as $k => $qval) {
			if ($qval == $qid) {
				$qids_new[$k] = $qids[$k + 1];
				$kk = $k + 1;
				$kv = $qval;
			} else {
				$qids_new[$k] = $qval;
			}
		}
		$qids_new[$kk] = $kv;
		$qids = array_filter(array_unique($qids_new));
		$qids = implode(",", $qids);
		$userdata = array(
			'qids' => $qids
		);
		$this->db->where('quid', $quid);
		$this->db->update('savsoft_quiz', $userdata);
	}
	function get_qcl($quid)
	{
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_qcl');
		return $query->result_array();
	}
	function remove_qid($quid, $qid)
	{
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_quiz');
		$quiz = $query->row_array();
		$new_qid = array();
		foreach (explode(',', $quiz['qids']) as $key => $oqid) {
			if ($oqid != $qid) {
				$new_qid[] = $oqid;
			}
		}
		$noq = count($new_qid);
		$userdata = array(
			'qids' => implode(',', $new_qid),
			'noq' => $noq
		);
		$this->db->where('quid', $quid);
		$this->db->update('savsoft_quiz', $userdata);
		return true;
	}
	function add_qid($quid, $qid)
	{
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_quiz');
		$quiz = $query->row_array();
		$new_qid = array();
		foreach (explode(',', $quiz['qids']) as $key => $oqid) {
			if ($oqid != $qid) {
				$new_qid[] = $oqid;
			}
		}
		$new_qid[] = $qid;
		$new_qid = array_filter(array_unique($new_qid));
		$noq = count($new_qid);
		$userdata = array(
			'qids' => implode(',', $new_qid),
			'noq' => $noq
		);
		$this->db->where('quid', $quid);
		$this->db->update('savsoft_quiz', $userdata);
		return true;
	}
	function get_quiz($quid)
	{
		$this->db->select('*,savsoft_quiz.quid as squid');
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_quiz');
		return $query->row_array();
	}
	function remove_quiz($quid)
	{
		$this->db->where('quid', $quid);
		if ($this->db->delete('savsoft_quiz')) {
			return true;
		} else {
			return false;
		}
	}
	function count_result($quid, $uid)
	{
		$this->db->where('quid', $quid);
		$this->db->where('uid', $uid);
		$query = $this->db->get('savsoft_result');
		return $query->num_rows();
	}
	function insert_result($quid, $uid, $theme)
	{
		// get quiz info
		$this->db->where('quid', $quid);
		$query = $this->db->get('savsoft_quiz');
		$quiz = $query->row_array();
		if ($quiz['question_selection'] == '0') {
			// get questions
			$noq = $quiz['noq'];
			$qids = explode(',', $quiz['qids']);
			$qids = $this->new_qids_active_list($qids);
			$categories = array();
			$category_range = array();
			$i = 0;
			$wqids = implode(',', $qids);
			$noq = array();
			$query = $this->db->query("select * from savsoft_qbank join savsoft_category on savsoft_category.cid=savsoft_qbank.cid where qid in ($wqids) ORDER BY FIELD(qid,$wqids)  ");
			$questions = $query->result_array();
			foreach ($questions as $qk => $question) {
				if (!in_array($question['category_name'], $categories)) {
					if (count($categories) != 0) {
						$i += 1;
					}
					$categories[] = $question['category_name'];
					$noq[$i] += 1;
				} else {
					$noq[$i] += 1;
				}
			}
			$categories = array();
			$category_range = array();
			$i = -1;
			foreach ($questions as $qk => $question) {
				if (!in_array($question['category_name'], $categories)) {
					$categories[] = $question['category_name'];
					$i += 1;
					$category_range[] = $noq[$i];
				}
			}
		} else {
			$userData = $this->user_model->get_user($uid);
			$userCategory = $this->db->where("FIND_IN_SET('".$userData['gid']."', savsoft_category.category_gids)")->get('savsoft_category')->row_array();
			$category_name = $userCategory['category_name'];
			$gid = $userData['gid'];
			$num_practice_done = $this->result_model->count_user_practice_quiz($uid, $gid, $category_name);
			// randomaly select qids
			$this->db->where('quid', $quid);
			$query = $this->db->get('savsoft_qcl');
			$qcl = $query->result_array();
			$qids = array();
			$categories = array();
			$category_range = array();
			$didQuestionsArray = $this->qbank_model->get_user_done_questions_qid_by_category($userData['uid'], $userCategory['cid']);
			foreach ($qcl as $k => $val) {
				$cid = $val['cid'];
				$lid = $val['lid'];
				$noq = $val['noq'];
				$i = 0;
				$arr_lid = explode(",",$lid);
				$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
				// Lấy các câu hỏi đã làm
				if(count($didQuestionsArray) > 0) {
					$this->db->where_not_in('qid', $didQuestionsArray);
				}
				if($num_practice_done < 10){
					// Lấy các câu hỏi có độ khó dưới 60%
					$list_low_diff = $this->qbank_model->get_low_difficulty_question($cid);
					$qidList = array_column($list_low_diff, "qid");
					$arrDone = $didQuestionsArray;
					$diff = array_diff($qidList, $arrDone);
					if(count($diff) > $noq){
						$this->db->where_in('qid', $diff);
					}else if(count($diff) <  $noq && count($diff) > 0){
						$noq = $noq - count($diff);
						$this->db->where_not_in('qid', $diff);
						$addQids = $diff;
					}
				}
				if($cid != "0") {
					$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
				}
				if($lid != 0){
					foreach($arr_lid as $arr => $lids){
						$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
					}
				}
				$this->db->order_by('rand()');
				$this->db->limit($noq);
				$query = $this->db->where('status', 1)->get('savsoft_qbank');
				//$query = $this->db->query("select * from savsoft_qbank join savsoft_category on savsoft_category.cid=savsoft_qbank.cid where FIND_IN_SET('".$cid."', savsoft_qbank.cid) and FIND_IN_SET('1', savsoft_qbank.lid) and FIND_IN_SET('7', savsoft_qbank.lid) ORDER BY RAND() limit $noq ");
				$questions = $query->result_array();
				foreach ($questions as $qk => $question) {
					$qids[] = $question['qid'];
					if (!in_array($question['category_name'], $categories)) {
						$categories[] = $question['category_name'];
						$category_range[] = $i + $noq;
					}
				}
				
				if(isset($addQids)) {
					$qids = array_merge($qids, $addQids);
				}
				$qids = $this->filter_disabled_question($qids);

				// Nếu chưa đủ số câu thì sẽ lấy câu đã làm rồi
				if(count($qids) < $noq && count($didQuestionsArray) > 0) {
					$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
					if(count($didQuestionsArray) > 0){
						$this->db->where_in('qid', $didQuestionsArray);
					}
					if(count($qids) > 0){
						$this->db->where_not_in('qid', $qids);
					}
					if($cid != 0) {
						$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
					}
					if($lid != 0){
						foreach($arr_lid as $arr => $lids){
							$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
						}
					}
					$this->db->order_by('rand()');
					$this->db->limit($noq - count($qids));
					$query = $this->db->where('status', 1)->get('savsoft_qbank');
					$questions = $query->result_array();
					foreach ($questions as $qk => $question) {
						$qids[] = $question['qid'];
						if (!in_array($question['category_name'], $categories)) {
							$categories[] = $question['category_name'];
							$category_range[] = $i + $noq;
						}
					}
				}
				// check câu hỏi liên tiếp theo nhóm
				$qids = $this->check_question_group($qids);
				if(count($qids) < $noq) {
					$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
					$this->db->where_not_in('qid', $qids);
					if($cid != 0) {
						$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
					}
					if($lid != 0){
						foreach($arr_lid as $arr => $lids){
							$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
						}
					}
					$this->db->order_by('rand()');
					$this->db->limit($noq - count($qids));
					$query = $this->db->where('status', 1)->get('savsoft_qbank');
					$questions = $query->result_array();
					foreach ($questions as $qk => $question) {
						$qids[] = $question['qid'];
						if (!in_array($question['category_name'], $categories)) {
							$categories[] = $question['category_name'];
							$category_range[] = $i + $noq;
						}
					}
				}
			}
		}
		//check quiz noq
		if(count($qids) == 0){
			$this->session->set_flashdata('message', "<div class='alert alert-danger'>" . $this->lang->line('quiz_not_available') . " </div>");
			redirect('quiz/quiz_detail/' . $quid);
		}/* else if(count($qids) < $quiz['noq']){
			$message = sprintf(
				"<div class='alert alert-danger'>%s, %s: %d/%d</div>",
				$this->lang->line('quiz_not_available'),
				$this->lang->line('num_question'),
				count($qids),
				$quiz['noq']
			);
			$this->session->set_flashdata('message', $message);
			return "Error";
		} */
		$zeros = array();
		foreach ($qids as $qidval) {
			$zeros[] = 0;
		}
		$userdata = array(
			'quid' => $quid,
			'uid' => $uid,
			'r_qids' => implode(',', $qids),
			'categories' => implode(',', $categories),
			'category_range' => implode(',', $category_range),
			'start_time' => time(),
			'individual_time' => implode(',', $zeros),
			'score_individual' => implode(',', $zeros),
			'attempted_ip' => $_SERVER['REMOTE_ADDR'],
			'theme' => $theme
		);
		if ($this->session->userdata('photoname')) {
			$photoname = $this->session->userdata('photoname');
			$userdata['photo'] = $photoname;
		}
		$this->db->insert('savsoft_result', $userdata);
		$rid = $this->db->insert_id();
		return $rid;
	}

	function insert_retake_result($uid, $data){
		$zeros = array();
		foreach (explode(",",$data['individual_time']) as $qidval) {
			$zeros[] = 0;
		}

		$array_qids = explode(',', $data['r_qids']);
		shuffle($array_qids);
		$random_qids = implode(',', $array_qids);

		$userdata = array(
			'quid' => $data['quid'],
			'uid' => $uid,
			'r_qids' => $random_qids,
			'categories' => $data['categories'],
			'category_range' => $data['category_range'],
			'start_time' => time(),
			'individual_time' => implode(',', $zeros),
			'score_individual' => implode(',', $zeros),
			'attempted_ip' => $_SERVER['REMOTE_ADDR'],
			'theme' => $data['theme'],
		);
		if ($this->session->userdata('photoname')) {
			$photoname = $this->session->userdata('photoname');
			$userdata['photo'] = $photoname;
		}
		$this->db->insert('savsoft_result', $userdata);
		$rid = $this->db->insert_id();
		return $rid;
	}

	function insert_retake_result_practice($data, $result){
		$uid = $data['uid'];
		$zeros = array();
		foreach (explode(",",$result['individual_time']) as $qidval) {
			$zeros[] = 0;
		}
		$array_qids = explode(',', $result['r_qids']);
		shuffle($array_qids);
		$random_qids = implode(',', $array_qids);
		$userdata = array(
			'quid' => 0,
			'uid' => $uid,
			'r_qids' => $random_qids,
			'categories' => $result['categories'],
			'category_range' => $result['category_range'],
			'start_time' => time(),
			'individual_time' => implode(',', $zeros),
			'score_individual' => implode(',', $zeros),
			'attempted_ip' => $_SERVER['REMOTE_ADDR'],
			'theme' => 0,
			'domain' => str_replace(['%20', ','], [' ', ', '], $data['domain']),
			'characteristic' => $data['characteristic'],
			'practice_time' => $data['duration'],
			'practice_noq' => $data['noq'],
			'practice_name' => str_replace(['%20', ','], [' ', ', '], $data['name']),
		);
		if ($this->session->userdata('photoname')) {
			$photoname = $this->session->userdata('photoname');
			$userdata['photo'] = $photoname;
		}
		$this->db->insert('savsoft_result', $userdata);
		$rid = $this->db->insert_id();
		return $rid;
	}

	function filter_disabled_question($qids) {
		$disabledQuestions = $this->db->where_in('qid', $qids)->where('status', 0)->select('qid')->get('savsoft_qbank')->result_array();
		if(count($disabledQuestions) > 0) {
		  return array_filter($qids, function($qid) use ($disabledQuestions) {
			return !in_array($qid, array_column($disabledQuestions, 'qid'));
		  });
		}
		return $qids;
	}

	// check câu hỏi liên tiếp theo nhóm
	function check_question_group($qids) {
		$newQids = $qids;
		$numQuestion = count($qids);
		$hasNextQid = $this->db->where_in('qid', $qids)->where('next_qid !=', '')->where('status', 1)->get('savsoft_qbank')->result_array();
		$hasPrevQid = $this->db->where_in('next_qid', $qids)->where('status', 1)->get('savsoft_qbank')->result_array();
		$previousGroup = [];
		if(count($hasNextQid) == 0) {
			return $newQids;
		}
		foreach ($hasNextQid as $key => $value) {
			// xoá các câu có câu kế tiếp
			$newQids = array_diff($newQids, [$value['qid']]);
		}
		foreach ($hasPrevQid as $key => $value) {
			// xoá các câu có câu trước đó
			$newQids = array_diff($newQids, [$value['next_qid']]);
		}
		foreach ($hasNextQid as $key => $value) {
			if(in_array($value['qid'], $previousGroup)) {
				continue;
			}
			$groupNext = $this->get_next($value['next_qid'], [$value['qid'], $value['next_qid']]);
			$groupPrev = $this->get_previous($value['qid'], []);
			$group = [...$groupPrev, ...$groupNext];
			$previousGroup = $group;
			// nếu số câu liên tiếp nhiều hơn tổng số câu bài test
			if(count($group) > $numQuestion) return $newQids;
			$group = array_merge($group, $newQids);
			$newQids = array_slice($group, 0, $numQuestion);
		}
		return $newQids;
	}

	function get_next($next, $result) {
		// result ban đầu sẽ có câu 1 và 2
		$next = $this->db->where('qid', $next)->select('qid, next_qid')->get('savsoft_qbank')->row_array();
		if(isset($next['next_qid']) && $next['next_qid'] != '') {
			array_push($result, $next['next_qid']);
			$this->get_next($next['next_qid'], $result);
		}
		return $result;
	}

	function get_previous($prev, $result) {
		$prev = $this->db->where('next_qid', $prev)->where('status', 1)->select('qid, next_qid')->get('savsoft_qbank')->row_array();
		if(isset($prev)) {
			$result = [$prev['qid'], ...$result];
			$this->get_previous($prev['qid'], $result);
		}
		return $result;
	}

	function insert_result_practice($data)
	{
		$logged_in = $this->session->userdata('logged_in');
		$uid = $data['uid'];
		$userData = $this->user_model->get_user($uid);
		$userCategory = $this->db->where("FIND_IN_SET('".$userData['gid']."', savsoft_category.category_gids)")->get('savsoft_category')->row_array();
		$category_name = $userCategory['category_name'];
		$gid = $userData['gid'];
		$num_practice_done = $this->result_model->count_user_practice_quiz($uid, $gid, $category_name);
		$domain = [];
		$list_where_in = [];
		$data['characteristic'] = explode(',', $data['characteristic']);
		$data['difficulty'] = explode(',', $data['difficulty']);
		foreach (DOMAINS as $group => $domainName) {
			if ($this->user_model->check_user_in_group($uid, $group)) {
				$domain = $domainName;
				break;
			}
		}
		if(in_array('notattempted', $data['characteristic']) || in_array('all', $data['characteristic']) ) {
			// Lấy các câu hỏi đã làm
			$didQuestionsArray = $this->qbank_model->get_user_done_questions_qid_practice($uid, $domain, $data);
		}
		if(in_array('done_wrong', $data['characteristic'])) {
			$incorrect_arr = [];
			$cid = $this->user_model->getCategoryByGroup($logged_in['gid']);
			$query = $this->db->where('savsoft_answers.uid', $uid)->where("FIND_IN_SET('".$cid['cid']."', savsoft_qbank.cid)")->group_by('savsoft_answers.qid')->where("savsoft_result.result_status != 'Open'")->where("savsoft_result.result_status != 'Cancel'")->where("savsoft_qbank.status", 1)->group_by('savsoft_answers.rid')->join('savsoft_result', 'savsoft_result.rid = savsoft_answers.rid', 'left')->join('savsoft_quiz', 'savsoft_quiz.quid = savsoft_result.quid', 'left')->join('savsoft_qbank', 'savsoft_qbank.qid = savsoft_answers.qid', 'left')->select('*')->select_sum('score_u')->order_by('savsoft_answers.qid', 'DESC')->get('savsoft_answers');
			$data_questions = $query->result_array();
			foreach($data_questions as $val) {
				if($val['score_u'] < 0.99 && !in_array($val['qid'], $incorrect_arr)) {
					$incorrect_arr[] = $val['qid'];
				}
			}
		}
		if(in_array('was_bookmark', $data['characteristic'])) {
			$list = $this->result_model->get_result_list_by_uid($uid);
			$bookmarks = [];
			$unique_bookmark = [];
			foreach($list as $value => $key){
				//Tim Cau hoi danh dau
				$mark_question = explode(",",$key['saved_r']);
				$mark_index = [];
				foreach($mark_question as $marked => $marked_info){
					if($marked_info != "null" && $marked_info != "" && !in_array($marked_info,$mark_index)){
						array_push($mark_index ,$marked_info);
					}
				}
				//info cac cau sai
				$each_question = explode(",",$key['r_qids']);
				foreach($each_question as $question => $info){
					$get_question = $this->quiz_model->get_questions_exam_tool($info);
					if(in_array($question, $mark_index) && $get_question != null){
						if(!in_array($each_question[$question], $unique_bookmark)){
							array_push($unique_bookmark,$each_question[$question]);
							$get_question[0]["quid"] = $key["quid"];
							array_push($bookmarks ,$get_question[0]['qid']);
						}
					}
				}
			}
		}
			// randomaly select qids
			// $this->db->where('quid', $quid);
			// $query = $this->db->get('savsoft_qcl');
			// $qcl = $query->result_array();
			// $qids = array();
			$categories = array();
			$category_range = array();

			// get category
			$userData = $this->db->where('uid', $uid)->get('savsoft_users')->row_array();
			$category = $this->db->where("FIND_IN_SET('".$userData['gid']."', savsoft_category.category_gids)")->get('savsoft_category')->row_array();
			if($category != null) {
				$cid = $category['cid'];
			} else {
				$cid = "0";
			}
			if($data['domain'] != "0") {
				$data['domain'] = str_replace('%20', ' ', $data['domain']);
				$lid = [];
				$domainArr = explode(',', $data['domain']);
				foreach ($domainArr as $key => $value) {
					$domainlid = $domain[$value];
					array_push($lid, ...$domainlid);
				}
				$lid = implode(',', $lid);
			} else {
				$lid = 0;
			}
			$noq = $data['noq'];
			$i = 0;
			$arr_lid = explode(",",$lid);
			//test
			if(count($data['difficulty']) == 4 && (in_array('notattempted', $data['characteristic']) || in_array('all', $data['characteristic']))){
				//TH random
				//check làm dưới 10 bài ko
				if($num_practice_done < 10){
					// Lấy các câu hỏi có độ khó dưới 60%
					$list_low_diff = $this->qbank_model->get_low_difficulty_question_practice($cid, $uid, $data, $gid);
					$qidList = array_column($list_low_diff, "qid");
					if(!isset($didQuestionsArray)) {
						$arrDone = $this->qbank_model->get_user_done_questions_qid_practice($uid, $domain, $data);
					}else{
						$arrDone = $didQuestionsArray;
					}
					$diff = array_diff($qidList, $arrDone);
					if(count($diff) > $noq){
						array_push($list_where_in, ...$diff);
					}else if(count($diff) <  $noq && count($diff) > 0){
						$noq = $noq - count($diff);
						$this->db->where_not_in('qid', $diff);
						$addQids = $diff;
					}
				}
				//end
			}
			//query
			// $this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
			if ((isset($didQuestionsArray) && count($didQuestionsArray) > 0) || (isset($incorrect_arr) && count($incorrect_arr) > 0) || (isset($bookmarks) && count($bookmarks) > 0) || count($list_where_in) > 0) {
				$this->db->group_start();
				if(count($list_where_in) > 0) {
					$this->db->or_where_in('qid', $list_where_in);
				}
				if(isset($didQuestionsArray) && count($didQuestionsArray) > 0) {
					$this->db->where_not_in('qid', $didQuestionsArray);
				}else if(in_array('notattempted', $data['characteristic'])){
					$this->db->where('1=1');
				}
				if(isset($incorrect_arr) && $incorrect_arr != null && count($incorrect_arr) > 0) {
					$this->db->or_where_in('qid', $incorrect_arr);
				}
				if(isset($bookmarks) && $bookmarks != null && count($bookmarks) > 0) {
					$this->db->or_where_in('qid', $bookmarks);
				}
				$this->db->group_end();
			}
			if($cid != "0" && $cid != null) {
				$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
			}
			if($lid != 0){
				$this->db->group_start();
				foreach($arr_lid as $arr => $lids){
					$this->db->or_where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
				}
				$this->db->group_end();
			}
			if (count($data["difficulty"]) > 0) {
				$this->db->group_start();
				if (in_array("easy", $data["difficulty"])) {
					$this->db->or_group_start();
					$this->db->where("(savsoft_qbank.no_time_served - savsoft_qbank.no_time_unattempted) > 0");
					$this->db->where("(savsoft_qbank.no_time_incorrected + savsoft_qbank.no_time_corrected) > 10");
					$this->db->where("savsoft_qbank.no_time_corrected / (savsoft_qbank.no_time_served - savsoft_qbank.no_time_unattempted) >= 0.85");
					$this->db->group_end();
				}
				if (in_array("medium", $data["difficulty"])) {
					$this->db->or_group_start();
					$this->db->where("(savsoft_qbank.no_time_incorrected + savsoft_qbank.no_time_corrected) < 10");
					$this->db->or_where("savsoft_qbank.no_time_corrected / (savsoft_qbank.no_time_served - savsoft_qbank.no_time_unattempted) BETWEEN 0.7 AND 0.84");
					$this->db->group_end();
				}
				if (in_array("hard", $data["difficulty"])) {
					$this->db->or_group_start();
					$this->db->where("(savsoft_qbank.no_time_served - savsoft_qbank.no_time_unattempted) > 0");
					$this->db->where("(savsoft_qbank.no_time_incorrected + savsoft_qbank.no_time_corrected) > 10");
					$this->db->where("savsoft_qbank.no_time_corrected / (savsoft_qbank.no_time_served - savsoft_qbank.no_time_unattempted) BETWEEN 0.4 AND 0.69");
					$this->db->group_end();
				}
				if (in_array("very_hard", $data["difficulty"])) {
					$this->db->or_group_start();
					$this->db->where("(savsoft_qbank.no_time_served - savsoft_qbank.no_time_unattempted) > 0");
					$this->db->where("(savsoft_qbank.no_time_incorrected + savsoft_qbank.no_time_corrected) > 10");
					$this->db->where("savsoft_qbank.no_time_corrected / (savsoft_qbank.no_time_served - savsoft_qbank.no_time_unattempted) < 0.4");
					$this->db->group_end();
				}
				$this->db->group_end();
			}
			//$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
			$this->db->order_by('rand()');
			$this->db->limit($noq);
			$query = $this->db->where('savsoft_qbank.status', 1)->get('savsoft_qbank');
			//$query = $this->db->query("select * from savsoft_qbank join savsoft_category on savsoft_category.cid=savsoft_qbank.cid where FIND_IN_SET('".$cid."', savsoft_qbank.cid) and FIND_IN_SET('1', savsoft_qbank.lid) and FIND_IN_SET('7', savsoft_qbank.lid) ORDER BY RAND() limit $noq ");
			$questions = $query->result_array();
			// echo $this->db->last_query();die;

			foreach ($questions as $qk => $question) {
				$qids[] = $question['qid'];
				if (!in_array($question['category_name'], $categories)) {
					$categories[] = $question['category_name'];
					$category_range[] = $i + $noq;
				}
			}
			$qids = $this->check_question_group($qids);
			if(isset($addQids)) {
				$qids = array_merge($qids, $addQids);
			}
			$qids = $this->filter_disabled_question($qids);

			// Nếu chưa đủ số câu thì sẽ lấy câu đã làm rồi
			if (
				isset($didQuestionsArray) &&
				count($qids) < $noq &&
				count($didQuestionsArray) > 0 &&
				(!in_array('notattempted', $data['characteristic']) || in_array('all', $data['characteristic']))
			) {
				$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
				$this->db->where_in('qid', $didQuestionsArray);
				$this->db->where_not_in('qid', $qids);
				if($cid != "0" && $cid != null) {
					$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
				}
				if($lid != 0){
					foreach($arr_lid as $arr => $lids){
						$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
					}
				}
				$this->db->order_by('rand()');
				$this->db->limit($noq - count($qids));
				$query = $this->db->where('status', 1)->get('savsoft_qbank');
				$questions = $query->result_array();
				foreach ($questions as $qk => $question) {
					$qids[] = $question['qid'];
					if (!in_array($question['category_name'], $categories)) {
						$categories[] = $question['category_name'];
						$category_range[] = $i + $noq;
					}
				}
			}
		if(count($qids) < $data['noq']){
			$message = sprintf(
				"<div class='alert alert-danger'>%s, %s: %d/%d</div>",
				$this->lang->line('quiz_not_available'),
				$this->lang->line('num_question'),
				count($qids),
				$data['noq']
			);
			$this->session->set_flashdata('message', $message);
			return "Error";
		}
		$zeros = array();
		foreach ($qids as $qidval) {
			$zeros[] = 0;
		}
		$userdata = array(
			'quid' => 0,
			'uid' => $uid,
			'r_qids' => implode(',', $qids),
			'categories' => $category['category_name'],
			'category_range' => implode(',', $category_range),
			'start_time' => time(),
			'individual_time' => implode(',', $zeros),
			'score_individual' => implode(',', $zeros),
			'attempted_ip' => $_SERVER['REMOTE_ADDR'],
			'theme' => 0,
			'domain' => str_replace(['%20', ','], [' ', ', '], $data['domain']),
			'characteristic' => implode(',', $data['characteristic']),
			'practice_time' => $data['duration'],
			'practice_noq' => $data['noq'],
			'practice_name' => str_replace(['%20', ','], [' ', ', '], $data['name']),
		);
		if ($this->session->userdata('photoname')) {
			$photoname = $this->session->userdata('photoname');
			$userdata['photo'] = $photoname;
		}
		$this->db->insert('savsoft_result', $userdata);
		$rid = $this->db->insert_id();
		return $rid;
	}

	public function get_uid_by_appid($appid)
	{
		$this->db->where('appid', $appid);
		$query=$this->db->get('mobile_app_settings');
		$query = $query->row_array();
		return $query['userid'];
	}
	public function get_uid_by_email($email)
	{
		$this->db->where('email', $email);
		$query=$this->db->get('savsoft_users');
		$query = $query->row_array();
		return $query['uid'];
	}
	public function get_name_by_appid($appid)
	{
		$this->db->where('appid', $appid);
		$query=$this->db->get('mobile_app_settings');
		$query = $query->row_array();
		return $query['appname'];
	}

	function insert_result_from_app($data, $platform, $theme = 0)
	{
		// get quiz info
		$this->db->where('quid', (int)$data->quizId);
		$query = $this->db->get('savsoft_quiz');
		$quiz = $query->row_array();
		// get questions
		$noq = $quiz['noq'];
		$qids = $data->r_qids;
		// if($quiz['qids'] == "") {
		// 	$qids = $data->r_qids;
		// } else {
		// 	$qids = explode(',', $quiz['qids']);
		// }
		$categories = array();
		$category_range = array();
		$i = 0;
		$wqids = implode(',', $qids);
		$noq = array();
		$query = $this->db->query("select * from savsoft_qbank join savsoft_category on savsoft_category.cid=savsoft_qbank.cid where qid in ($wqids) ORDER BY FIELD(qid,$wqids)  ");
		$questions = $query->result_array();
		foreach ($questions as $qk => $question) {
			if (!in_array($question['category_name'], $categories)) {
				if (count($categories) != 0) {
					$i += 1;
				}
				$categories[] = $question['category_name'];
				$noq[$i] += 1;
			} else {
				$noq[$i] += 1;
			}
		}
		$categories = array();
		$category_range = array();
		$i = -1;
		foreach ($questions as $qk => $question) {
			if (!in_array($question['category_name'], $categories)) {
				$categories[] = $question['category_name'];
				$i += 1;
				$category_range[] = $noq[$i];
			}
		}

		$uid = $this->get_uid_by_appid($data->appid);

		$userdata = array(
			'quid' => (int)$data->quizId,
			'uid' => $uid,
			'r_qids' => implode(',', $qids),
			'categories' => implode(',', $categories),
			'category_range' => implode(',', $category_range),
			'start_time' => $data->start_time,
			'saved_r' => $data->bookmark_index,
			'end_time' => $data->end_time,
			'result_status' => $data->status,
			'score_obtained' => $data->score_obtained,
			'score_individual' => $data->score_individual,
			'percentage_obtained' => (float)$data->percentage,
			'total_time' => $data->total_time,
			'theme' => $theme,
			'app_username' => $data->app_username,
			'app_platform' => $platform,
			'app_version' => $data->app_version,
			'device_id' => $data->device_id,
			'premium' => $data->premium,
			'debug_id' => $_POST['debugId']
		);
		$this->db->insert('savsoft_result', $userdata);
		$rid = $this->db->insert_id();
		return $rid;
	}
	function insert_result_from_app_exam_tool($data, $platform, $theme = 0)
	{
		// get quiz info
		$this->db->where('quid', (int)$data->quizId);
		$query = $this->db->get('savsoft_quiz');
		$quiz = $query->row_array();
		// get questions
		$noq = $quiz['noq'];
		$qids = $data->r_qids;
		$categories = array();
		$category_range = array();
		$i = 0;
		$wqids = implode(',', $qids);
		$noq = array();
		$query = $this->db->query("select * from savsoft_qbank join savsoft_category on savsoft_category.cid=savsoft_qbank.cid where qid in ($wqids) ORDER BY FIELD(qid,$wqids)  ");
		$questions = $query->result_array();
		foreach ($questions as $qk => $question) {
			if (!in_array($question['category_name'], $categories)) {
				if (count($categories) != 0) {
					$i += 1;
				}
				$categories[] = $question['category_name'];
				$noq[$i] += 1;
			} else {
				$noq[$i] += 1;
			}
		}
		$categories = array();
		$category_range = array();
		$i = -1;
		foreach ($questions as $qk => $question) {
			if (!in_array($question['category_name'], $categories)) {
				$categories[] = $question['category_name'];
				$i += 1;
				$category_range[] = $noq[$i];
			}
		}
		$uid = $this->get_uid_by_email($data->username);
		$userdata = array(
			'quid' => (int)$data->quizId,
			'uid' => $data->uid ?? $uid,
			'r_qids' => implode(',', $qids),
			'categories' => implode(',', $categories),
			'category_range' => implode(',', $category_range),
			'start_time' => $data->start_time,
			'saved_r' => $data->bookmark_index,
			'end_time' => $data->end_time,
			'result_status' => $data->status,
			'score_obtained' => $data->score_obtained,
			'score_individual' => $data->score_individual,
			'percentage_obtained' => (float)$data->percentage,
			'total_time' => $data->total_time,
			'theme' => $theme,
			'app_username' => $data->app_username,
			'app_platform' => $platform,
			'app_version' => $data->app_version,
		);
		$this->db->insert('savsoft_result', $userdata);
		$rid = $this->db->insert_id();
		return $rid;
	}
	function insert_answer_from_app($data, $rid, $appid)
	{
		$uid = $this->get_uid_by_appid($appid);
		$query = $this->db->query("select * from savsoft_result join savsoft_quiz on savsoft_result.quid=savsoft_quiz.quid where savsoft_result.rid='$rid' ");
		$quiz = $query->row_array();
		$correct_score = $quiz['correct_score'];
		$incorrect_score = $quiz['incorrect_score'];
		$qids = explode(',', $quiz['r_qids']);
		// $disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
		// $disable_question = [];
		// foreach($disable_question_query as $val){
		// 	$disable_question[] = $val['qid'];
		// }
		// $qids = array_values(array_diff($qids, $disable_question));
		$vqids = $quiz['r_qids'];
		$correct_incorrect = explode(',', $quiz['score_individual']);
		// remove existing answers
		$qn = array_keys($data);
		$this->db->where('rid', $rid);
		$this->db->where_in('qn', $qn);
		$this->db->delete('savsoft_answers');
		foreach ($data as $ak => $answer) {
				// multiple choice single answer
					$qid = $qids[$ak];
					$query = $this->db->query(" select * from savsoft_options where qid='$qid' ");
					$options_data = $query->result_array();
					$options = array();
					foreach ($options_data as $ok => $option) {
						$options[$option['oid']] = $option['score'];
					}
					$attempted = 0;
					$marks = 0;
					foreach ($answer as $sk => $ansval) {
						if ($options[$ansval] <= 0) {
							$marks += -1;
						} else {
							$marks += $options[$ansval];
						}
						$userdata = array(
							'rid' => $rid,
							'qid' => $qid,
							'uid' => $uid,
							'qn' => $ak,
							'q_option' => $ansval,
							'score_u' => $options[$ansval]
						);
						$this->db->insert('savsoft_answers', $userdata);
						$attempted = 1;
					}
					$crin = "";
					if ($attempted == 1) {
						if ($marks >= '0.99') {
							$correct_incorrect[$ak] = 1;
							$crin = ", no_time_corrected=(no_time_corrected +1)";
						} else {
							$correct_incorrect[$ak] = 2;
							$crin = ", no_time_incorrected=(no_time_incorrected +1)";
						}
					} else {
						$correct_incorrect[$ak] = 0;
						$crin = ", no_time_unattempted=(no_time_unattempted +1) ";
					}
					$query_qp = "update savsoft_qbank set no_time_served=(no_time_served +1)  $crin  where qid='$qid'  ";
					$this->db->query($query_qp);
		}
		$userdata = array(
			'score_individual' => implode(',', $correct_incorrect),
			'individual_time' => $_POST['individual_time'],
		);
		$this->db->where('rid', $rid);
		$this->db->update('savsoft_result', $userdata);
		return "Answer saved";
	}
	function insert_answer_from_app_exam_tool($data, $rid, $email)
	{
		$uid = $this->get_uid_by_email($email);
		$query = $this->db->query("select * from savsoft_result join savsoft_quiz on savsoft_result.quid=savsoft_quiz.quid where savsoft_result.rid='$rid' ");
		$quiz = $query->row_array();
		$correct_score = $quiz['correct_score'];
		$incorrect_score = $quiz['incorrect_score'];
		$qids = explode(',', $quiz['r_qids']);
		// $disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
		// $disable_question = [];
		// foreach($disable_question_query as $val){
		// 	$disable_question[] = $val['qid'];
		// }
		// $qids = array_values(array_diff($qids, $disable_question));
		$vqids = $quiz['r_qids'];
		$correct_incorrect = explode(',', $quiz['score_individual']);
		// remove existing answers
		$qn = array_keys($data);
		$this->db->where('rid', $rid);
		$this->db->where_in('qn', $qn);
		$this->db->delete('savsoft_answers');
		foreach ($data as $ak => $answer) {
				// multiple choice single answer
					$qid = $qids[$ak];
					$query = $this->db->query(" select * from savsoft_options where qid='$qid' ");
					$options_data = $query->result_array();
					$options = array();
					foreach ($options_data as $ok => $option) {
						$options[$option['oid']] = $option['score'];
					}
					$attempted = 0;
					$marks = 0;
					foreach ($answer as $sk => $ansval) {
						if($ansval != "0") {
							if ($options[$ansval] <= 0) {
								// $marks += -1;
							} else {
								$marks += $options[(int)$ansval];
							}
							$userdata = array(
								'rid' => $rid,
								'qid' => $qid,
								'uid' => $uid,
								'qn' => $ak,
								'q_option' => $ansval,
								'score_u' => $options[$ansval]
							);
							$this->db->insert('savsoft_answers', $userdata);
							$attempted = 1;
						}
					}
					$crin = "";
					if ($attempted == 1) {
						if ($marks >= '0.99') {
							$correct_incorrect[$ak] = 1;
							$crin = ", no_time_corrected=(no_time_corrected +1)";
						} else {
							$correct_incorrect[$ak] = 2;
							$crin = ", no_time_incorrected=(no_time_incorrected +1)";
						}
					} else {
						$correct_incorrect[$ak] = 0;
						$crin = ", no_time_unattempted=(no_time_unattempted +1) ";
					}
					$query_qp = "update savsoft_qbank set no_time_served=(no_time_served +1)  $crin  where qid='$qid'  ";
					$this->db->query($query_qp);
		}
		$userdata = array(
			// 'score_individual' => implode(',', $correct_incorrect),
			'individual_time' => $_POST['individual_time'],
		);
		$this->db->where('rid', $rid);
		$this->db->update('savsoft_result', $userdata);
		return "Answer saved";
	}

	function open_result($quid, $uid)
	{
		$result_open = $this->lang->line('open');
		$query = $this->db->query("select * from savsoft_result  where savsoft_result.result_status='$result_open'  and savsoft_result.uid='$uid'  ");
		if ($query->num_rows() >= '1') {
			$result = $query->row_array();
			return $out = [$result['rid'], $result['theme']];
		} else {
			return '0';
		}
	}
	function open_result_practice($uid)
	{
		$result_open = $this->lang->line('open');
		$query = $this->db->query("select rid from savsoft_result  where savsoft_result.result_status='$result_open'  and savsoft_result.uid='$uid'  ");
		if ($query->num_rows() >= '1') {
			$result = $query->row_array();
			return $out = $result['rid'];
		} else {
			return '0';
		}
	}
	function quiz_result($rid)
	{
		$query = $this->db->query("select * from savsoft_result join savsoft_quiz on savsoft_result.quid=savsoft_quiz.quid where savsoft_result.rid='$rid' ");
		return $query->row_array();
	}
	function quiz_result_practice($rid)
	{
		$query = $this->db->query("select * from savsoft_result where savsoft_result.rid='$rid' ");
		return $query->row_array();
	}
	function saved_answers($rid)
	{
		$query = $this->db->query("select * from savsoft_answers  where savsoft_answers.rid='$rid' ");
		return $query->result_array();
	}
	function saved_answers_for_report($uid,$qid){
		$this->db->select('*');
		$this->db->from('savsoft_answers');
		$this->db->where('uid', $uid);
		$this->db->where('qid', $qid);
		$this->db->order_by('aid', 'DESC');
		$query = $this->db->get();
		$result = $query->result_array();
		return $result;
	}

	function get_question_type($qid){
		$this->db->select('question_type');
		$this->db->select('description');
		$this->db->where('qid', $qid);
		$query = $this->db->get('savsoft_qbank');
		return $query->result_array();
	}
	function saved_answers_from_arr($rid)
	{
		$query = $this->db->where_in('rid', $rid)->get('savsoft_answers');
		return $query->result_array();
	}
	function removeEmpty($array,$value){
		foreach($array as $a => $b){
			if($b == ""){
				$array[$a] = $value;
			}
		}
		return $array;
	}
	function assign_score($rid, $qno, $score)
	{
		$qp_score = $score;
		$query = $this->db->query("select * from savsoft_result join savsoft_quiz on savsoft_result.quid=savsoft_quiz.quid where savsoft_result.rid='$rid' ");
		$quiz = $query->row_array();
		$score_ind = explode(',', $quiz['score_individual']);
		$score_ind[$qno] = $score;
		$r_qids = explode(',', $quiz['r_qids']);
		$correct_score = $this->removeEmpty(explode(',', $quiz['correct_score']),"1");
		$incorrect_score = $this->removeEmpty(explode(',', $quiz['incorrect_score']),"0");
		$manual_valuation = 0;
		$marks = 0;
		foreach ($score_ind as $mk => $score) {
			if ($score == 1) {
				if (isset($correct_score[$mk])) {
					$marks += $correct_score[$mk];
				} else {
					$marks += $correct_score[0];
				}
			}
			if ($score == 2) {
				if (isset($correct_score[$mk])) {
					$marks += $incorrect_score[$mk];
				} else {
					$marks += $incorrect_score[0];
				}
			}
			if ($score == 3) {
				$manual_valuation = 1;
			}
		}
		$percentage_obtained = ($marks / (array_sum($correct_score))) * 100;
		if ($percentage_obtained >= $quiz['pass_percentage']) {
			$qr = $this->lang->line('pass');
		} else {
			$qr = $this->lang->line('fail');
		}
		$userdata = array(
			'score_individual' => implode(',', $score_ind),
			'score_obtained' => $marks,
			'percentage_obtained' => $percentage_obtained,
			'manual_valuation' => $manual_valuation
		);
		if ($manual_valuation == 1) {
			$userdata['result_status'] = $this->lang->line('pending');
		} else {
			$userdata['result_status'] = $qr;
		}
		$this->db->where('rid', $rid);
		$this->db->update('savsoft_result', $userdata);
		// question performance
		$qp = $r_qids[$qno];
		$crin = "";
		if ($$qp_score == '1') {
			$crin = ", no_time_corrected=(no_time_corrected +1)";
		} else if ($$qp_score == '2') {
			$crin = ", no_time_incorrected=(no_time_incorrected +1)";
		}
		$query_qp = "update savsoft_qbank set  $crin  where qid='$qp'  ";
		$this->db->query($query_qp);
	}
	function submit_result($rid = null)
	{
		if($rid == null) {
			if (!$this->session->userdata('logged_in')) {
				$logged_in = $this->session->userdata('logged_in_raw');
			} else {
				$logged_in = $this->session->userdata('logged_in');
			}
			$email = $logged_in['email'];
			$rid = $this->session->userdata('rid');
		}
		$query = $this->db->query("select * from savsoft_result
	left join savsoft_quiz on savsoft_result.quid=savsoft_quiz.quid
	join savsoft_users on savsoft_users.uid=savsoft_result.uid
	where savsoft_result.rid='$rid' ");
		$quiz = $query->row_array();
		if($quiz['noq'] == null) $quiz['noq'] = $quiz['practice_noq'];
		$score_ind = explode(',', $quiz['score_individual']);
		$r_qids = explode(',', $quiz['r_qids']);
		$qids_perf = array();
		$marks = 0;
		$correct_score = explode(',', $quiz['correct_score']);
		$incorrect_score = explode(',', $quiz['incorrect_score']);
		$total_time = array_sum(explode(',', $quiz['individual_time']));
		$manual_valuation = 0;
		// $disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
		// $disable_question = [];
		// $num_disable_question_in_quiz = $this->count_disable_question_in_quiz($quiz['r_qids']);
		// foreach($disable_question_query as $val){
		// 	if (($key = array_search($val['qid'], $r_qids)) !== false) {
		// 		$num_disable_question_in_quiz++;
		// 	}
		// }
		if($quiz['correct_score'] == null) {
			$correct_score = array_fill(0, $quiz['noq'], 1);
			$incorrect_score = array_fill(0, $quiz['noq'], 0);
		}
		if($quiz['pass_percentage'] == null) {
			$quiz['pass_percentage'] = PRACTICE_PASS;
		}
		foreach ($score_ind as $mk => $score) {
			$qids_perf[$r_qids[$mk]] = $score;
			if ($score == 1) {
				if (isset($correct_score[$mk])) {
					$marks += $correct_score[$mk];
				} else {
					$marks += $correct_score[0];
				}
			}
			if ($score == 2) {
				if (isset($incorrect_score[$mk])) {
					$marks += $incorrect_score[$mk];
				} else {
					$marks += $incorrect_score[0];
				}
			}
			if ($score == 3) {
				$manual_valuation = 1;
			}
		}
		$result_data = $this->result_model->get_result($rid);
		$total_questions = count(explode(",",$result_data['r_qids']));
		if (is_array($correct_score)) {
			$percentage_obtained = ($marks / ($total_questions)) * 100;
		} else {
			$percentage_obtained = ($marks / (($total_questions) * $correct_score)) * 100;
		}
		if ($percentage_obtained >= $quiz['pass_percentage']) {
			$qr = $this->lang->line('pass');
		} else {
			$qr = $this->lang->line('fail');
		}
		$userdata = array(
			'total_time' => $total_time,
			'end_time' => time(),
			'score_obtained' => $marks,
			'percentage_obtained' => $percentage_obtained,
			'manual_valuation' => $manual_valuation
		);
		if ($manual_valuation == 1) {
			$userdata['result_status'] = $this->lang->line('pending');
		} else {
			$userdata['result_status'] = $qr;
		}
		$this->db->where('rid', $rid);
		$this->db->update('savsoft_result', $userdata);
		foreach ($qids_perf as $qp => $qpval) {
			$crin = "";
			if ($qpval == '0') {
				$crin = ", no_time_unattempted=(no_time_unattempted +1) ";
			} else if ($qpval == '1') {
				$crin = ", no_time_corrected=(no_time_corrected +1)";
			} else if ($qpval == '2') {
				$crin = ", no_time_incorrected=(no_time_incorrected +1)";
			}
			$query_qp = "update savsoft_qbank set no_time_served=(no_time_served +1)  $crin  where qid='$qp'  ";
			$this->db->query($query_qp);
		}
		if ($this->config->item('allow_result_email') == "true") {
			$this->load->library('email');
			$query = $this->db->query("select savsoft_result.*,savsoft_users.*,savsoft_quiz.* from savsoft_result, savsoft_users, savsoft_quiz where savsoft_users.uid=savsoft_result.uid and savsoft_quiz.quid=savsoft_result.quid and savsoft_result.rid='$rid'");
			$qrr = $query->row_array();
			if ($this->config->item('protocol') == "smtp") {
				$config['protocol'] = 'smtp';
				$config['smtp_host'] = $this->config->item('smtp_hostname');
				$config['smtp_user'] = $this->config->item('smtp_username');
				$config['smtp_pass'] = $this->config->item('smtp_password');
				$config['smtp_port'] = $this->config->item('smtp_port');
				$config['smtp_timeout'] = $this->config->item('smtp_timeout');
				$config['mailtype'] = $this->config->item('smtp_mailtype');
				$config['starttls']  = $this->config->item('starttls');
				$config['newline']  = $this->config->item('newline');
				$config['smtp_crypto'] = $this->config->item('smtp_crypto');
				$this->email->initialize($config);
			}
			$toemail = $qrr['email'];
			$fromemail = $this->config->item('fromemail');
			$fromname = $this->config->item('fromname');
			$subject = $this->config->item('result_subject');
			$message = $this->config->item('result_message');
			$subject = str_replace('[email]', $qrr['email'], $subject);
			$subject = str_replace('[name]', $qrr['name'], $subject);
			$subject = str_replace('[quiz_name]', $qrr['quiz_name'], $subject);
			$subject = str_replace('[score_obtained]', $qrr['score_obtained'], $subject);
			$subject = str_replace('[percentage_obtained]', $qrr['percentage_obtained'], $subject);
			$subject = str_replace('[current_date]', date('Y-m-d H:i:s', time()), $subject);
			$subject = str_replace('[result_status]', $qrr['result_status'], $subject);
			$message = str_replace('[email]', $qrr['email'], $message);
			$message = str_replace('[name]', $qrr['name'], $message);
			$message = str_replace('[quiz_name]', $qrr['quiz_name'], $message);
			$message = str_replace('[score_obtained]', $qrr['score_obtained'], $message);
			$message = str_replace('[percentage_obtained]', $qrr['percentage_obtained'], $message);
			$message = str_replace('[current_date]', date('Y-m-d H:i:s', time()), $message);
			$message = str_replace('[result_status]', $qrr['result_status'], $message);
			$this->email->to($toemail);
			$this->email->from($fromemail, $fromname);
			$this->email->subject($subject);
			$this->email->message($message);
			if (!$this->email->send()) {
				//print_r($this->email->print_debugger());
			}
		}
		// feed ends
		return true;
	}

	public function remove_answer($checkLogin = true)
	{
		$rid = $_POST['rid'];
		$qn = $_POST['qn'];
		if($checkLogin) {
			$srid = $this->session->userdata('rid');
			if (!$this->session->userdata('logged_in')) {
				$logged_in = $this->session->userdata('logged_in_raw');
			} else {
				$logged_in = $this->session->userdata('logged_in');
			}
			$uid = $logged_in['uid'];
			if ($srid != $rid) {
				return "Something wrong";
			}
		}
		$query = $this->db->where('rid', $rid)->where('qn', $qn)->delete('savsoft_answers');
		$query1 = $this->db->query("select * from savsoft_result where savsoft_result.rid='$rid' ");
		$quiz = $query1->row_array();
		$correct_incorrect = explode(',', $quiz['score_individual']);
		$correct_incorrect[$qn] = 0;
		$userdata = array(
			'score_individual' => implode(',', $correct_incorrect),
			// 'individual_time' => $_POST['individual_time'],
		);
		$this->db->where('rid', $rid);
		$this->db->update('savsoft_result', $userdata);
		if($query) {
			echo 'success';
		} else {
			echo 'error';
		}
	}

	function insert_answer($checkLogin = true, $uid = 0)
	{
		$rid = $_POST['rid'];
		if($checkLogin) {
			$srid = $this->session->userdata('rid');
			if (!$this->session->userdata('logged_in')) {
				$logged_in = $this->session->userdata('logged_in_raw');
			} else {
				$logged_in = $this->session->userdata('logged_in');
			}
			$uid = $logged_in['uid'];
			if ($srid != $rid) {
				return "Something wrong";
			}
		}
		$query = $this->db->query("select * from savsoft_result join savsoft_quiz on savsoft_result.quid=savsoft_quiz.quid where savsoft_result.rid='$rid' ");
		$quiz = $query->row_array();
		// Practice
		if($quiz == null) {
			$quiz = $this->quiz_result_practice($rid);
		}
		$qids = explode(',', $quiz['r_qids']);
		$disable_question_query = $this->db->where('status', 0)->where_in('qid', $qids)->select('qid')->get('savsoft_qbank')->result_array();
		$disable_question = [];
		foreach($disable_question_query as $val){
			$disable_question[] = $val['qid'];
		}
		$qids = array_values(array_diff($qids, $disable_question));
		$correct_incorrect = explode(',', $quiz['score_individual']);
		$qn = $_POST['qn'];
		// remove existing answers
		$this->db->where('rid', $rid);
		$this->db->where('qn', $qn);
		$this->db->delete('savsoft_answers');
		$answer = $_POST['answer'];
		$answer = explode(',', $answer);
				// multiple choice single answer
				if ($_POST['question_type'] == '1' || $_POST['question_type'] == '2') {
					$qid = $qids[$qn];
					$query = $this->db->query(" select * from savsoft_options where qid='$qid' ");
					$options_data = $query->result_array();
					$options = array();
					foreach ($options_data as $ok => $option) {
						$options[$option['oid']] = $option['score'];
					}
					$attempted = 0;
					$marks = 0;
					foreach ($answer as $sk => $ansval) {
						if ($options[$ansval] <= 0) {
							$marks += -1;
						} else {
							$marks += $options[$ansval];
						}
						$userdata = array(
							'rid' => $rid,
							'qid' => $qid,
							'uid' => $uid,
							'qn' => $qn,
							'q_option' => $ansval,
							'score_u' => $options[$ansval]
						);
						$this->db->insert('savsoft_answers', $userdata);
						$attempted = 1;
					}
					if ($attempted == 1) {
						if ($marks >= '0.99') {
							$correct_incorrect[$qn] = 1;
						} else {
							$correct_incorrect[$qn] = 2;
						}
					} else {
						$correct_incorrect[$qn] = 0;
					}
				}
				// short answer
				if ($_POST['question_type'] == '3') {
					$qid = $qids[$qn];
					$query = $this->db->query(" select * from savsoft_options where qid='$qid' ");
					$options_data = $query->row_array();
					$options_data = explode(',', $options_data['q_option']);
					$noptions = array();
					foreach ($options_data as $op) {
						$noptions[] = strtoupper(trim($op));
					}
					$attempted = 0;
					$marks = 0;
					foreach ($answer as $sk => $ansval) {
						if ($ansval != '') {
							if (in_array(strtoupper(trim($ansval)), $noptions)) {
								$marks = 1;
							} else {
								$marks = 0;
							}
							$attempted = 1;
							$userdata = array(
								'rid' => $rid,
								'qid' => $qid,
								'uid' => $uid,
								'qn' => $qn,
								'q_option' => $ansval,
								'score_u' => $marks
							);
							$this->db->insert('savsoft_answers', $userdata);
						}
					}
					if ($attempted == 1) {
						if ($marks == 1) {
							$correct_incorrect[$qn] = 1;
						} else {
							$correct_incorrect[$qn] = 2;
						}
					} else {
						$correct_incorrect[$qn] = 0;
					}
				}
				// long answer
				if ($_POST['question_type'] == '4') {
					$attempted = 0;
					$marks = 0;
					$qid = $qids[$qn];
					foreach ($answer as $sk => $ansval) {
						if ($ansval != '') {
							$userdata = array(
								'rid' => $rid,
								'qid' => $qid,
								'uid' => $uid,
								'qn' => $qn,
								'q_option' => $ansval,
								'score_u' => 0
							);
							$this->db->insert('savsoft_answers', $userdata);
							$attempted = 1;
						}
					}
					if ($attempted == 1) {
						$correct_incorrect[$qn] = 3;
					} else {
						$correct_incorrect[$qn] = 0;
					}
				}
				// match
				if ($_POST['question_type'] == '5') {
					$qid = $qids[$qn];
					$query = $this->db->query(" select * from savsoft_options where qid='$qid' ");
					$options_data = $query->result_array();
					$noptions = array();
					foreach ($options_data as $op => $option) {
						$noptions[] = $option['q_option'] . '___' . $option['q_option_match'];
					}
					$marks = 0;
					$attempted = 0;
						if ($answer != '0') {
							$mc = 0;
							if (in_array($answer, $noptions)) {
								$marks += 1 / count($options_data);
								$mc = 1 / count($options_data);
							} else {
								$marks += 0;
								$mc = 0;
							}
							$userdata = array(
								'rid' => $rid,
								'qid' => $qid,
								'uid' => $uid,
								'qn' => $qn,
								'q_option' => $answer,
								'score_u' => $mc
							);
							$this->db->insert('savsoft_answers', $userdata);
							$attempted = 1;
						}
					if ($attempted == 1) {
						if ($marks == 1) {
							$correct_incorrect[$qn] = 1;
						} else {
							$correct_incorrect[$qn] = 2;
						}
					} else {
						$correct_incorrect[$qn] = 0;
					}
				}
		$userdata = array(
			'score_individual' => implode(',', $correct_incorrect),
			'individual_time' => $_POST['individual_time'],
		);
		$this->db->where('rid', $rid);
		$this->db->update('savsoft_result', $userdata);
		return "Answer saved";
	}
	function set_ind_time($rid)
	{
		$userdata = array(
			'individual_time' => $_POST['individual_time'],
		);
		$this->db->where('rid', $rid);
		$this->db->update('savsoft_result', $userdata);
		return true;
	}
	function validateLicesne()
	{
		// Add License key
		$license_key = $this->config->item('sq_license_key');
		// Add directory path of your script with respect to file where you are going/added this code
		$dir_paths = getcwd();
		$curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_RETURNTRANSFER => 1, CURLOPT_URL => 'https://savsoftquiz.com/license/verify_key.php',  CURLOPT_USERAGENT => 'Codular Sample cURL Request',   CURLOPT_POST => 1,
			CURLOPT_POSTFIELDS => array('license_key' => $license_key,  'server_data' => json_encode($_SERVER))
		));
		$resp = curl_exec($curl);
		curl_close($curl);
		if (trim($resp) == "DELETE") {
			delete_files($dir_paths);
		}
		if (is_object(json_decode(trim($resp)))) {
			$rep = json_decode(trim($resp));
			if ($rep->status_res == "VERIFIED" ||  $rep->status_res == "VERIFIED_P") {
				if ($rep->status_res == "VERIFIED_P") {
					echo "<script> 	let stateCheck = setInterval(() => {   if (document.readyState === 'complete') {    clearInterval(stateCheck);
    // document ready
 if(typeof jQuery == 'undefined'){
        var oScriptElem = document.createElement('script');
        oScriptElem.type = 'text/javascript';
        oScriptElem.src ='https://code.jquery.com/jquery-3.2.1.js';
        document.head.insertBefore(oScriptElem, document.head.getElementsByTagName('script')[0])
   } setTimeout(function(){
   var val='<a class=lpt href=" . $rep->poweredbyurl . " >" . $rep->poweredbytext . "</a>';
$('body').append(val);
},3000); }}, 100);</script>";
					echo "<style>.lpt{ " . $rep->poweredbystyle . " } </style>";
				}
			}
		} else {
			exit($resp);
		}
		function delete_files($target)
		{
			if (is_dir($target)) {
				$files = glob($target . '*', GLOB_MARK); //GLOB_MARK adds a slash to directories returned
				foreach ($files as $file) {
					delete_files($file);
				}
				rmdir($target);
			} elseif (is_file($target)) {
				unlink($target);
			}
		}
	}
	public function get_all_quiz()
	{
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	public function order_in_group($gid)
	{
		$this->db->where("FIND_IN_SET(".$gid.", gids)");
		$this->db->where("quiz_order.group_id",$gid);
		$this->db->join('quiz_order','quiz_order.exam_id = savsoft_quiz.quid', 'left');
		$this->db->order_by('order_number');
		$this->db->group_by('quid');
		$query = $this->db->get('savsoft_quiz');
		return $query->result_array();
	}
	public function quiz_of_group($group)
	{
		$this->db->where("FIND_IN_SET(".$group.", gids)");
		$this->db->order_by('quid', 'desc');
		$this->db->select('quid');
		$query = $this->db->get('savsoft_quiz');
		$result = $query->result_array();
		foreach($result as $value) {
			$out[] = $value['quid']; 
		}
		return $out;
	}
	public function check_saved($rid, $answer)
	{
		foreach($answer as $ans) {
			$this->db->where('rid', $rid);
			$this->db->where('qn', $ans);
			$query = $this->db->get('savsoft_answers');
			$result = $query->result_array();
			if($result == null) {
				$out[] = $ans; 
			}
		}
		return implode(',', $out);
	}

	public function get_list_qids($id)
	{
		$this->db->where('quid', $id);
		$query = $this->db->get('savsoft_quiz');
		$result = $query->result_array();
		if($result[0]['qids'] != "") {
			$qids = explode(',', $result[0]['qids']);
			$disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
			$disable_question = [];
			foreach($disable_question_query as $val){
				$disable_question[] = $val['qid'];
			}
			$qids = array_values(array_diff($qids, $disable_question));
			return implode(",", $qids);
		} else {
			// randomaly select qids
			$this->db->where('quid', $id);
			$query = $this->db->get('savsoft_qcl');
			$qcl = $query->result_array();
			$qids = array();
			$categories = array();
			$category_range = array();
			foreach ($qcl as $k => $val) {
				$cid = $val['cid'];
				$lid = $val['lid'];
				$noq = $val['noq'];
				$i = 0;
				$arr_lid = explode(",",$lid);
				$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
				if($cid != 0) {
					$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
				}
				if($lid != 0){
					foreach($arr_lid as $arr => $lids){
						$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
					}
				}
				$this->db->order_by('rand()');
				$this->db->limit($noq);
				$query = $this->db->where('status', 1)->get('savsoft_qbank');
				//$query = $this->db->query("select * from savsoft_qbank join savsoft_category on savsoft_category.cid=savsoft_qbank.cid where FIND_IN_SET('".$cid."', savsoft_qbank.cid) and FIND_IN_SET('1', savsoft_qbank.lid) and FIND_IN_SET('7', savsoft_qbank.lid) ORDER BY RAND() limit $noq ");
				$questions = $query->result_array();
				foreach ($questions as $qk => $question) {
					$qids[] = $question['qid'];
				}

				// check câu hỏi liên tiếp theo nhóm
				$qids = $this->check_question_group($qids);
				if(count($qids) < $noq) {
					$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
					$this->db->where_not_in('qid', $qids);
					if($cid != 0) {
						$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
					}
					if($lid != 0){
						foreach($arr_lid as $arr => $lids){
							$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
						}
					}
					$this->db->order_by('rand()');
					$this->db->limit($noq - count($qids));
					$query = $this->db->where('status', 1)->get('savsoft_qbank');
					$questions = $query->result_array();
					foreach ($questions as $qk => $question) {
						$qids[] = $question['qid'];
					}
				}
			}
			return implode(",", $qids);
		}
	}

	public function get_list_qids_fill_did_quiz($id, $userId)
	{
		$this->db->where('quid', $id);
		$query = $this->db->get('savsoft_quiz');
		$result = $query->result_array();
		$quiz = $result[0];
		if($quiz['qids'] != "") {
			// Bài test fixed
			$qids = explode(',', $result[0]['qids']);
			$disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
			$disable_question = [];
			foreach($disable_question_query as $val){
				$disable_question[] = $val['qid'];
			}
			$qids = array_values(array_diff($qids, $disable_question));
			return implode(",", $qids);
		} else {
			// randomaly select qids
			// Lấy các câu hỏi đã làm
			$didQuestions = $this->db->where('result_status !=', 'Open')->where('result_status !=', 'Cancel')->where('uid', $userId)->select('r_qids')->get('savsoft_result')->result_array();
			$didQuestionsArray = [];
			foreach($didQuestions as $value) {
				array_push($didQuestionsArray, ...explode(',', $value['r_qids']));
			}
			// Lọc trùng lặp câu đã làm
			$didQuestionsArray = array_unique($didQuestionsArray);
			$this->db->where('quid', $id);
			$query = $this->db->get('savsoft_qcl');
			$qcl = $query->result_array();
			$qids = array();
			$categories = array();
			$category_range = array();
			foreach ($qcl as $k => $val) {
				$cid = $val['cid'];
				$lid = $val['lid'];
				$noq = $val['noq'];
				$i = 0;
				$arr_lid = explode(",",$lid);
				// $this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
				// Lọc các câu đã làm
				if(count($didQuestionsArray) > 0) {
					$this->db->where_not_in('qid', $didQuestionsArray);
				}
				if($cid != 0) {
					$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
				}
				if($lid != 0){
					foreach($arr_lid as $arr => $lids){
						$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
					}
				}
				$this->db->order_by('rand()');
				$this->db->limit($noq);
				$query = $this->db->where('status', 1)->get('savsoft_qbank');
				//$query = $this->db->query("select * from savsoft_qbank join savsoft_category on savsoft_category.cid=savsoft_qbank.cid where FIND_IN_SET('".$cid."', savsoft_qbank.cid) and FIND_IN_SET('1', savsoft_qbank.lid) and FIND_IN_SET('7', savsoft_qbank.lid) ORDER BY RAND() limit $noq ");
				$questions = $query->result_array();
				foreach ($questions as $qk => $question) {
					$qids[] = $question['qid'];
				}
				// Nếu chưa đủ số câu thì sẽ lấy câu đã làm rồi
				if(count($qids) < $noq) {
					// $this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
					$this->db->where_in('qid', $didQuestionsArray);
					if($cid != 0) {
						$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
					}
					if($lid != 0){
						foreach($arr_lid as $arr => $lids){
							$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
						}
					}
					$this->db->order_by('rand()');
					$this->db->limit($noq - count($qids));
					$query = $this->db->where('status', 1)->get('savsoft_qbank');
					$questions = $query->result_array();
					foreach ($questions as $qk => $question) {
						$qids[] = $question['qid'];
					}
				}

				// check câu hỏi liên tiếp theo nhóm
				$qids = $this->check_question_group($qids);
				if(count($qids) < $noq) {
					$this->db->join('savsoft_category','savsoft_category.cid = savsoft_qbank.cid');
					$this->db->where_not_in('qid', $qids);
					if($cid != 0) {
						$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");
					}
					if($lid != 0){
						foreach($arr_lid as $arr => $lids){
							$this->db->where("FIND_IN_SET('".$lids."', savsoft_qbank.lid)");
						}
					}
					$this->db->order_by('rand()');
					$this->db->limit($noq - count($qids));
					$query = $this->db->where('status', 1)->get('savsoft_qbank');
					$questions = $query->result_array();
					foreach ($questions as $qk => $question) {
						$qids[] = $question['qid'];
					}
				}
			}
			return implode(",", $qids);
		}
	}

	function count_disable_question()
	{
		$count_disable_question = [];
		$dataQuiz = $this->quiz_list(0);
		$disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
		$disable_question = [];
		foreach($disable_question_query as $val){
			$disable_question[] = $val['qid'];
		}
		foreach($dataQuiz as $value) {
			$count = 0;
			if($value['qids'] != ''){
				$qids = explode(',', $value['qids']);
				foreach($qids as $val) {
					if(in_array($val, $disable_question)) $count++;
				}
				$count_disable_question[$value['quid']] = $count;
			}
		}
		return $count_disable_question;
	}

	function count_disable_question_app($dataQuiz)
	{
		$count_disable_question = [];
		$disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
		$disable_question = [];
		foreach($disable_question_query as $val){
			$disable_question[] = $val['qid'];
		}
		foreach($dataQuiz as $value) {
			$count = 0;
			if($value['qids'] != ''){
				$qids = explode(',', $value['qids']);
				foreach($qids as $val) {
					if(in_array($val, $disable_question)) $count++;
				}
				$count_disable_question[$value['quid']] = $count;
			}
		}
		return $count_disable_question;
	}

	function count_disable_question_in_quiz($list_qid)
	{
		$query = $this->db->query("select * FROM savsoft_qbank WHERE qid IN (".$list_qid.") AND status = 0");
		return $query->num_rows();
	}

	function count_disable_question_detail($quid)
	{
		$count = 0;
		$query = $this->db->where('quid', $quid)->get('savsoft_quiz')->row_array();
		if($query['qids'] != ''){
			$disable_question_query = $this->db->where('status', 0)->get('savsoft_qbank')->result_array();
			$disable_question = [];
			foreach($disable_question_query as $val){
				$disable_question[] = $val['qid'];
			}
			$qids = explode(',', $query['qids']);
			foreach($qids as $val) {
				if(in_array($val, $disable_question)) $count++;
			}
		}
		return $count;
	}
	function count_unique_question_done($uid, $gid, $category){
		$list = [];
		$query = $this->db->query("
			SELECT 
			savsoft_result.score_individual, 
			savsoft_result.rid, 
			savsoft_result.r_qids,
			savsoft_result.percentage_obtained
			FROM 
				savsoft_result
			LEFT JOIN 
				savsoft_quiz 
			ON 
				savsoft_quiz.quid = savsoft_result.quid
			WHERE 
				savsoft_result.uid = '$uid'
				AND (
					FIND_IN_SET('$gid', savsoft_quiz.gids)
					OR (
						FIND_IN_SET('$category', savsoft_result.categories) 
						AND savsoft_result.quid = 0
					)
				)
				AND NOT (
					savsoft_result.percentage_obtained < 10 
					AND savsoft_result.total_time < 120
				)
				AND savsoft_result.result_status NOT IN ('Open', 'Cancel')
			ORDER BY 
				savsoft_result.rid DESC;");
		$result = $query->result_array();
		foreach($result as $val => $value){
			$arr = explode(",",$value['score_individual']);
			$arr2 = explode(",",$value['r_qids']);
			foreach($arr as $ar => $ar_val){
				if($ar_val ==  '1' || $ar_val == '2' ){
					if (!in_array($arr2[$ar], $list))
					array_push($list,$arr2[$ar]);
				}
			}
		}
		return count($list);
	}
	function count_category_question($gid){
		$this->db->where("FIND_IN_SET('".$gid."', category_gids)");
		$this->db->limit(1);
		$query = $this->db->get('savsoft_category');
		if($query->num_rows()){
			$cid = $query->row()->category_name;
			switch($cid){
				case "PSM-I":
					return 600;
					break;
				case "PSM-II":
					return 500;
					break;
				case "PSPO-I":
					return 800;
					break;
				case "PSPO-II":
					return 400;
					break;
				case "SPS":
					return 400;
					break;
				case "PSK":
					return 400;
					break;
				case "PSD":
					return 600;
					break;
				case "ACP":
					return 1500;
					break;
				case "PMP":
					return 1500;
					break;
				case "CCBA":
					return 1500;
					break;
				case "CBAP":
					return 1500;
					break;
				case "PAL-I":
					return 300;
					break;
				case "PAL-EBM":
					return 300;
					break;
				case "PSU":
					return 200;
					break;
				case "PgMP":
					return 700;
					break;
				case "ISTQB Foundation":
					return 800;
					break;
				case "AWS Certified Solutions Architect - Associate (SAA-C03)":
					return 500;
					break;
				case "ECBA":
					return 500;
					break;
				case "CAPM":
					return 800;
					break;
				case "PMI-RMP":
					return 500;
					break;
				case "IIBA-AAC":
					return 300;
					break;
				case "ITIL Foundation":
					return 500;
					break;
				case "PMI-PBA":
					return 800;
					break;
				case "PfMP":
					return 1000;
					break;
				case "PMI-RMP":
					return 1000;
					break;
				case "PMI-SP":
					return 1000;
					break;
				default:
					return 400;
					break;
			}
			/* $this->db->where('cid',$cid);
			$query2 = $this->db->get('savsoft_qbank');
			$num = $query2->num_rows(); */
			
		}else{
			return 600;
		}	
	}

	function get_group_name($gid){
		$this->db->where('gid', $gid);
		$query = $this->db->get('savsoft_group');
		return $query->row()->group_name;
	}

	function get_comment($data){
		$logged_in = $this->session->userdata('logged_in');
		$uid = $logged_in['uid'];
		if(count($data) != 0){
			$qids = $data;
			$this->db->select("savsoft_comment.*");
			$this->db->select("savsoft_users.name, savsoft_users.photo, savsoft_users.su");
			$this->db->select("COUNT(savsoft_likes_history.id) as likes");
			$this->db->select("(CASE WHEN COUNT(CASE WHEN savsoft_likes_history.uid = $uid THEN 1 ELSE NULL END) > 0 THEN TRUE ELSE FALSE END) as user_liked");
			$this->db->where_in('savsoft_comment.qid', $qids);
			$this->db->where('savsoft_comment.status', 1);
			$this->db->group_by('savsoft_comment.id');
			$this->db->group_by('savsoft_users.name');
			$this->db->group_by('savsoft_users.photo'); 
			$this->db->order_by('savsoft_comment.id', 'asc');
			$this->db->join('savsoft_users', 'savsoft_users.uid = savsoft_comment.uid', 'left');
			$this->db->join('savsoft_likes_history', 'savsoft_likes_history.comment_id = savsoft_comment.id', 'left');
			$query = $this->db->get('savsoft_comment');
			$comments = $query->result_array();

			$comment_tree = [];
			foreach ($comments as $comment) {
				if ($comment['response_id'] === NULL) {
					$comment['reply'] = [];
					$comment_tree[] = $comment;
				} else {
					$ids = array_column($comment_tree, 'id');
					$index = array_search($comment['response_id'], $ids);
					if($comment['response_id'] == $comment_tree[$index]['id'])
					array_push($comment_tree[$index]['reply'], $comment);
				}
			}

			$comments_by_qid = [];

			foreach ($comment_tree as $comment) {
				$qid = $comment['qid'];
				if (!isset($comments_by_qid[$qid])) {
					$comments_by_qid[$qid] = [];
				}
				$comments_by_qid[$qid][] = $comment;
			}
			
			return $comments_by_qid;
		}else{
			return [];
		}
	}

	function get_questions_result($qids, $isApp = false, $uid = null)
	{
		if ($qids == '') {
			$qids = 0;
		} else {
			// Chuyển chuỗi qids thành mảng duy nhất
			$qidArray = array_unique(explode(',', $qids));
			$qids = implode(',', $qidArray);
		}
	
		// Lấy danh sách câu hỏi từ bảng savsoft_qbank
		$query = $this->db->query("SELECT *, SUBSTRING(description, 1, 100) AS shortened_description 
								   FROM savsoft_qbank 
								   WHERE savsoft_qbank.qid IN ($qids) 
								   ORDER BY FIELD(savsoft_qbank.qid, $qids)");
		$listQuestions = $query->result_array();
	
		if (!$isApp) {
			// Lấy mức độ khó của mỗi câu hỏi
			foreach ($listQuestions as $key => $val) {
				$difficultyStr = $this->result_model->getDifficultyStr($val);
				$listQuestions[$key]["difficulty"] = $difficultyStr;
			}
	
			if ($uid !== null) {
				$qidList = array_column($listQuestions, 'qid');
				
				if (!empty($qidList)) {
					$this->db->select('savsoft_answers.qid, savsoft_answers.rid');
					$this->db->from('savsoft_answers');
					$this->db->join('savsoft_result', 'savsoft_result.rid = savsoft_answers.rid', 'left');
					$this->db->where('savsoft_answers.uid', $uid);
					$this->db->where_not_in('savsoft_result.result_status', ['Open', 'Cancel']);
					$this->db->where_in('savsoft_answers.qid', $qidList);
					$this->db->group_by(['savsoft_answers.qid', 'savsoft_answers.rid']);
					$this->db->having('SUM(score_u) <', 0.99);
					$incorrects = $this->db->get()->result_array();
			
					$incorrectMap = [];
					foreach ($incorrects as $row) {
						$qid = $row['qid'];
						if (!isset($incorrectMap[$qid])) {
							$incorrectMap[$qid] = 1;
						} else {
							$incorrectMap[$qid]++;
						}
					}
			
					foreach ($listQuestions as $key => $val) {
						$qid = $val['qid'];
						$listQuestions[$key]['count'] = $incorrectMap[$qid] ?? 0;
					}
				}
			}
			
		}
	
		return $listQuestions;
	}
	
	function check_active_questions($qids){
		$qid_array = explode(',', $qids);

		$this->db->where_in('qid', $qid_array);
		$this->db->where('status', 1);
		$query = $this->db->get('savsoft_qbank');
		$num = $query->num_rows();
		return $num;
	}
	public function delete_result($rid)
	{
		$this->db->where('rid', $rid);
		if ($this->db->delete('savsoft_result')) {
			return true;
		} else {
			return false;
		}
	}
	public function new_qids_active_list($qids)
	{
		$qids_str = implode(',', $qids);
		$this->db->select('qid');
		$this->db->where_in('qid', $qids);
		$this->db->where('status', 1);
		$this->db->order_by("FIELD(qid, $qids_str)", null, false);
		$query = $this->db->get('savsoft_qbank');
		$result = array_column($query->result_array(), 'qid');
		return $result;
	}

	function get_recent_result_score($list){
		$result_list = [];
		$count_pass_recent = 0;
		foreach($list as $key => $val) {
			if(count($result_list) < 10){
				if($val["result_status"] == "Pass"){
					$count_pass_recent ++;
				}
				if($val['score_obtained'] >= 10 && $val['total_time'] >= 120){
					array_push($result_list, $val);
				}
			}else{
				break;
			}
		}
		if($count_pass_recent >= 8){
			$pass = 100;
		}else{
			$pass = $count_pass_recent * 100 / 8;
		}
		return $pass;
	}
	public function get_question_by_qid($qid)
	{
		$this->db->select('question');
		$this->db->where('qid', $qid);
		$query = $this->db->get('savsoft_qbank');
		if ($query->num_rows() > 0) {
			return $query->row()->question;
		} else {
			return ""; 
		}
	}
}
